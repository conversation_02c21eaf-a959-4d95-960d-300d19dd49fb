.settings {
  padding: 20px;
  overflow: auto;
}

.avatar {
  cursor: pointer;
  position: relative;
  z-index: 1;
}

.edit-prompt-modal {
  display: flex;
  flex-direction: column;

  .edit-prompt-title {
    max-width: unset;
    margin-bottom: 20px;
    text-align: left;
  }
  .edit-prompt-content {
    max-width: unset;
  }
}

.user-prompt-modal {
  min-height: 40vh;

  .user-prompt-search {
    width: 100%;
    max-width: 100%;
    margin-bottom: 10px;
    background-color: var(--gray);
  }

  .user-prompt-list {
    border: var(--border-in-light);
    border-radius: 10px;

    .user-prompt-item {
      display: flex;
      justify-content: space-between;
      padding: 10px;

      &:not(:last-child) {
        border-bottom: var(--border-in-light);
      }

      .user-prompt-header {
        max-width: calc(100% - 100px);

        .user-prompt-title {
          font-size: 14px;
          line-height: 2;
          font-weight: bold;
        }
        .user-prompt-content {
          font-size: 12px;
        }
      }

      .user-prompt-buttons {
        display: flex;
        align-items: center;
        column-gap: 2px;

        .user-prompt-button {
          //height: 100%;
          padding: 7px;
        }
      }
    }
  }
}

.subtitle-button {
  button {
    overflow:visible ;
  }
}

.custom-model-container {
  display: flex;
  width: 100%;
  gap: 10px;
  
  .custom-model-input {
    flex: 1;
    max-width: unset;
    text-align: left;
    min-width: 60%;
  }
  
  .custom-model-button {
    flex-shrink: 0;
  }
}

@media (max-width: 600px) {
  .settings {
    button {
      padding: 8px !important;
    }
    
    .user-prompt-button,
    .window-action-button button,
    .edit-prompt-modal button {
      padding: 8px !important;
    }
    
    :global(.icon-button) {
      padding: 8px !important;
    }
    
    .custom-model-container {
      gap: 5px;
      
      .custom-model-input {
        min-width: 50%;
      }
      
      .custom-model-button {
        padding: 6px !important;
        font-size: 12px;
      }
    }
  }
}
