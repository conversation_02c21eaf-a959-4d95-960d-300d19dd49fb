.file-attachment {
  display: inline-block;
  margin: 8px 0;
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
  }
  
  .file-attachment-card {
    display: flex;
    align-items: center;
    background-color: var(--gray);
    border-radius: 8px;
    padding: 12px;
    max-width: 100%;
    border: 1px solid var(--border-in-light);
  }
  
  .file-attachment-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: var(--primary);
    background-color: rgba(var(--primary-rgb), 0.1);
    border-radius: 6px;
    flex-shrink: 0;
  }
  
  .file-attachment-info {
    overflow: hidden;
    flex: 1;
  }
  
  .file-attachment-name {
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
    color: var(--black);
  }
  
  .file-attachment-size, .file-attachment-type {
    font-size: 12px;
    color: var(--black-50);
    margin-top: 2px;
  }
} 