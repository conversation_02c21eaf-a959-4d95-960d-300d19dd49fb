<svg width="44" height="44" viewBox="0 0 360 360" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#chatgpt_svg__a)" clip-path="url(#chatgpt_svg__b)">
        <path d="M0 180C0 80.589 80.589 0 180 0s180 80.589 180 180-80.589 180-180 180S0 279.411 0 180Z"
              fill="#315EF8"></path>
        <g filter="url(#chatgpt_svg__c)">
            <g filter="url(#chatgpt_svg__d)">
                <rect x="235.508" y="83.212" width="77.099" height="173.079" rx="38.55"
                      transform="rotate(21.987 235.508 83.212)" fill="url(#chatgpt_svg__e)"></rect>
            </g>
            <g filter="url(#chatgpt_svg__f)">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M102.234 136.443c.015-.587-1.004-.812-1.204-.261l-29.164 80.126c-7.281 20.007 3.034 42.128 23.04 49.41 20.007 7.282 42.128-3.034 49.41-23.04a57.396 57.396 0 0 0-12.924-59.786l-18.193-18.581a38.453 38.453 0 0 1-10.965-27.868Z"
                      fill="url(#chatgpt_svg__g)"></path>
            </g>
            <g filter="url(#chatgpt_svg__h)">
                <path d="M249.598 195.489a38.857 38.857 0 0 1 10.652 28.746c-1.729 34.097-43.435 49.55-66.963 24.812l-80.228-84.353a38.856 38.856 0 0 1-10.651-28.746c1.728-34.096 43.434-49.55 66.963-24.812l80.227 84.353Z"
                      fill="#fff" fill-opacity="0.85"></path>
            </g>
        </g>
    </g>
    <defs>
        <filter id="chatgpt_svg__a" x="-15.027" y="-11.27" width="390.053" height="390.053" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"></feColorMatrix>
            <feOffset dy="3.757"></feOffset>
            <feGaussianBlur stdDeviation="7.513"></feGaussianBlur>
            <feComposite in2="hardAlpha" operator="out"></feComposite>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"></feColorMatrix>
            <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_29_689"></feBlend>
            <feBlend in="SourceGraphic" in2="effect1_dropShadow_29_689" result="shape"></feBlend>
        </filter>
        <filter id="chatgpt_svg__c" x="62.038" y="91.568" width="240.837" height="188.208" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"></feColorMatrix>
            <feOffset dy="4.23"></feOffset>
            <feGaussianBlur stdDeviation="3.747"></feGaussianBlur>
            <feComposite in2="hardAlpha" operator="out"></feComposite>
            <feColorMatrix values="0 0 0 0 0.248635 0 0 0 0 0.245404 0 0 0 0 0.40694 0 0 0 0.15 0"></feColorMatrix>
            <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_29_689"></feBlend>
            <feBlend in="SourceGraphic" in2="effect1_dropShadow_29_689" result="shape"></feBlend>
        </filter>
        <filter id="chatgpt_svg__d" x="182.327" y="94.832" width="117.733" height="174.48" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"></feColorMatrix>
            <feOffset dx="4.678" dy="9.356"></feOffset>
            <feGaussianBlur stdDeviation="4.181"></feGaussianBlur>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"></feComposite>
            <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.39 0"></feColorMatrix>
            <feBlend in2="shape" result="effect1_innerShadow_29_689"></feBlend>
        </filter>
        <filter id="chatgpt_svg__f" x="69.531" y="134.697" width="85.382" height="133.356" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"></feColorMatrix>
            <feOffset dx="8.772" dy="-1.17"></feOffset>
            <feGaussianBlur stdDeviation="3.567"></feGaussianBlur>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"></feComposite>
            <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.51 0"></feColorMatrix>
            <feBlend in2="shape" result="effect1_innerShadow_29_689"></feBlend>
        </filter>
        <filter id="chatgpt_svg__h" x="101.149" y="97.138" width="159.15" height="165.621" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"></feColorMatrix>
            <feOffset dx="-1.209" dy="-10.877"></feOffset>
            <feGaussianBlur stdDeviation="0.928"></feGaussianBlur>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.103312 0 0 0 0 0.340187 0 0 0 0 0.949292 0 0 0 0.05 0"></feColorMatrix>
            <feBlend mode="multiply" in2="shape" result="effect1_innerShadow_29_689"></feBlend>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"></feColorMatrix>
            <feOffset dx="-1.209" dy="3.022"></feOffset>
            <feGaussianBlur stdDeviation="0.786"></feGaussianBlur>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"></feComposite>
            <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.94 0"></feColorMatrix>
            <feBlend in2="effect1_innerShadow_29_689" result="effect2_innerShadow_29_689"></feBlend>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"></feColorMatrix>
            <feOffset dx="-0.302" dy="0.604"></feOffset>
            <feGaussianBlur stdDeviation="0.302"></feGaussianBlur>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.729181 0 0 0 0 0.803822 0 0 0 0 0.995754 0 0 0 0.86 0"></feColorMatrix>
            <feBlend mode="hard-light" in2="effect2_innerShadow_29_689" result="effect3_innerShadow_29_689"></feBlend>
        </filter>
        <linearGradient id="chatgpt_svg__e" x1="289.153" y1="98.392" x2="200.569" y2="280.117"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff"></stop>
            <stop offset="1" stop-color="#fff" stop-opacity="0.62"></stop>
        </linearGradient>
        <linearGradient id="chatgpt_svg__g" x1="106.933" y1="134.422" x2="109.673" y2="254.984"
                        gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff" stop-opacity="0.75"></stop>
            <stop offset="1" stop-color="#fff"></stop>
        </linearGradient>
        <clipPath id="chatgpt_svg__b">
            <path fill="#fff" d="M0 0h360v360H0z"></path>
        </clipPath>
    </defs>
</svg>
