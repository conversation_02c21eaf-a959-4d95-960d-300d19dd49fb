version: "3.9"
services:
  NeatChat:
    profiles: [ "no-proxy" ]
    container_name: NeatChat
    image: tianzhentech/chatgpt-next-web:latest
    ports:
      - 3000:3000
    environment:
      - OPENAI_API_KEY=$OPENAI_API_KEY
      - GOOGLE_API_KEY=$GOOGLE_API_KEY
      - CODE=$CODE
      - BASE_URL=$BASE_URL
      - OPENAI_ORG_ID=$OPENAI_ORG_ID
      - HIDE_USER_API_KEY=$HIDE_USER_API_KEY
      - DISABLE_GPT4=$DISABLE_GPT4
      - ENABLE_BALANCE_QUERY=$ENABLE_BALANCE_QUERY
      - DISABLE_FAST_LINK=$DISABLE_FAST_LINK
      - OPENAI_SB=$OPENAI_SB

  NeatChat-proxy:
    profiles: [ "proxy" ]
    container_name: NeatChat-proxy
    image: tianzhentech/chatgpt-next-web:latest
    ports:
      - 3000:3000
    environment:
      - OPENAI_API_KEY=$OPENAI_API_KEY
      - GOOGLE_API_KEY=$GOOGLE_API_KEY
      - CODE=$CODE
      - PROXY_URL=$PROXY_URL
      - BASE_URL=$BASE_URL
      - OPENAI_ORG_ID=$OPENAI_ORG_ID
      - HIDE_USER_API_KEY=$HIDE_USER_API_KEY
      - DISABLE_GPT4=$DISABLE_GPT4
      - ENABLE_BALANCE_QUERY=$ENABLE_BALANCE_QUERY
      - DISABLE_FAST_LINK=$DISABLE_FAST_LINK
      - OPENAI_SB=$OPENAI_SB
