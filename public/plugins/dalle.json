{"openapi": "3.1.0", "info": {"title": "Dalle3", "version": "1.0.0"}, "servers": [{"url": "https://api.openai.com"}], "paths": {"/v1/images/generations": {"post": {"operationId": "Dalle3", "x-openai-isConsequential": false, "summary": "openai's dall-e image generator.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["model", "n", "prompt", "size", "quality", "style"], "properties": {"model": {"type": "string", "description": "model name, required and value is `dall-e-3`."}, "n": {"type": "number", "description": "value is `1`"}, "prompt": {"type": "string", "description": "A text description of the desired image(s). input must be a english prompt."}, "size": {"type": "string", "description": "images size, can be `1024x1024`, `1024x1792`, `1792x1024`. default value is `1024x1024`"}, "quality": {"type": "string", "description": "images quality, can be `standard`, `hd`. default value is `hd`"}, "style": {"type": "string", "description": "images style, can be `vivid`, `natural`. default value is `vivid`"}}}}}}}}}}